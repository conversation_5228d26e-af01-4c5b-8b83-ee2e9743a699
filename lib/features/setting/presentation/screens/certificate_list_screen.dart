import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import '../widgets/congratulatory_widget.dart';
import '../widgets/certificate_card.dart';

class CertificateListScreen extends StatelessWidget {
  const CertificateListScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final certificates = [
      {'title': 'CEFR Level B2', 'date': '15 June 2024'},
      {'title': 'CEFR Level B1', 'date': '28 January 2024'},
      {'title': 'CEFR Level A2', 'date': '03 September 2023'},
    ];

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black54),
          onPressed: () => context.pop(),
        ),
        title: Text(
          context.loc.certificate_list,
          style: const TextStyle(
            color: Colors.black87,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: certificates.length + 1,
        itemBuilder: (context, index) {
          if (index == 0) {
            return const Padding(
              padding: EdgeInsets.only(bottom: 16.0),
              child: CongratulatoryWidget(),
            );
          }
          final certificate = certificates[index - 1];
          return Padding(
            padding: const EdgeInsets.only(bottom: 10.0),
            child: CertificateCard(
              title: certificate['title']!,
              issueDate: certificate['date']!,
            ),
          );
        },
      ),
    );
  }
}
