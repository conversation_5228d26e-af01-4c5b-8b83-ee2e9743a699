import 'package:cached_network_image/cached_network_image.dart';
import 'package:change_case/change_case.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart'; // Import package_info_plus
import 'package:selfeng/configs/routes/core_router_name.dart'; // Added import
import 'package:selfeng/features/authentication/presentation/providers/auth_controller.dart'; // Added import
import 'package:selfeng/shared/helpers/navigation_helper.dart';
import 'package:selfeng/shared/localization/app_localizations_context.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';
import 'package:selfeng/services/setting_cache_service/domain/providers/setting_cache_provider.dart'; // Added provider import

// Define a theme color based on the image (approximation)
const Color kPrimaryThemeColor = Color(0xFFD93622); // Red color from image

class ProfileSettingsScreen extends ConsumerStatefulWidget {
  const ProfileSettingsScreen({super.key});

  @override
  ConsumerState<ProfileSettingsScreen> createState() =>
      _ProfileSettingsScreenState();
}

class _ProfileSettingsScreenState extends ConsumerState<ProfileSettingsScreen> {
  // Removed local state for sound toggle

  // Placeholder user data (replace with actual data fetching if needed)
  // Using FutureBuilder might be better in a real app, but simplifying for now
  final firebase_auth.User? _currentUser =
      firebase_auth.FirebaseAuth.instance.currentUser;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black54),
          onPressed: () => context.pop(),
        ),
        title: Text(
          context.loc.profile_settings, // Title from the image
          style: TextStyle(color: Colors.black87, fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.white, // Or a very light grey
        elevation: 0, // Subtle shadow
        centerTitle: true,
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(vertical: 16.0),
        children: [
          _buildProfileSection(context, _currentUser),
          const SizedBox(height: 24),
          _buildSectionHeader(context, 'Profile'), // Header from image
          _buildProfileList(context),
          const SizedBox(height: 24),
          _buildSectionHeader(
            context,
            context.loc.membership,
          ), // Header from image
          _buildMembershipList(context),
          const SizedBox(height: 24),
          _buildSectionHeader(
            context,
            context.loc.settings,
          ), // Header from image
          _buildSettingsList(context),
          const SizedBox(height: 24),
          _buildLogoutTile(context),
          const SizedBox(height: 24), // Adjusted spacing
          _buildVersionInfo(context), // Add version info display
          const SizedBox(height: 32), // Keep bottom padding
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, firebase_auth.User? user) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: Colors.grey.shade300,
            child:
                user?.photoURL != null && user!.photoURL!.isNotEmpty
                    ? ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: user.photoURL!,
                        placeholder: (context, url) => const LoadingCircle(),
                        errorWidget:
                            (context, url, error) => const Icon(
                              Icons.person,
                              size: 50,
                              color: Colors.grey,
                            ),
                        fit: BoxFit.cover,
                        width: 100,
                        height: 100,
                      ),
                    )
                    : const Icon(Icons.person, size: 50, color: Colors.grey),
          ),
          const SizedBox(height: 12),
          Text(
            user?.displayName?.toTitleCase() ??
                'Nama Pengguna', // Placeholder name
            style: Theme.of(
              context,
            ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 4),
          Text(
            user?.email ?? '<EMAIL>', // Placeholder email
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey.shade600),
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement Edit Profile Action
              print('Edit Profile Tapped');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: kPrimaryThemeColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            ),
            child: Text(context.loc.edit_profile),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          color: kPrimaryThemeColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildProfileList(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: Column(
          children: [
            _buildSettingsTile(
              context: context,
              icon: Icons.school, // Icon for Certificate
              title: 'Certificate', // Text for Certificate
              trailing: const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey,
              ),
              onTap: () {
                customNav(context, RouterName.certificateListScreen);
              },
            ),
            const Divider(height: 1, indent: 56), // Add divider
            _buildSettingsTile(
              context: context,
              icon: Icons.bookmark, // Icon for Bookmarks
              title: 'Bookmarks', // Text for Bookmarks
              trailing: const Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: Colors.grey,
              ),
              onTap: () {
                // TODO: Implement Bookmarks Navigation
                print('Bookmarks Tapped');
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSettingsList(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildSettingsTile(
            context: context,
            icon: Icons.language,
            title: context.loc.language, // Text from image
            trailing: const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: Colors.grey,
            ),
            onTap: () {
              customNav(
                context,
                RouterName.languageScreen,
                params: {'origin': 'profile'},
              );
            },
          ),
          const Divider(height: 1, indent: 56), // Add divider
          Consumer(
            builder: (context, ref, child) {
              final isAudioEnabled = ref.watch(audioToggleProvider);
              return _buildSettingsTile(
                context: context,
                icon:
                    isAudioEnabled
                        ? Icons.volume_up
                        : Icons.volume_off, // Use provider state for icon
                title: context.loc.sound, // Text from image
                trailing: Switch(
                  value: isAudioEnabled, // Use provider state for value
                  onChanged: (bool value) {
                    // Use provider notifier to update state
                    ref
                        .read(audioToggleProvider.notifier)
                        .setAudioEnabled(value);
                    // Removed setState
                  },
                  activeColor: kPrimaryThemeColor,
                ),
                onTap: null, // Switch handles interaction
              );
            },
          ),
          const Divider(height: 1, indent: 56), // Add divider
          _buildSettingsTile(
            context: context,
            icon: Icons.brightness_6, // Or Icons.dark_mode / Icons.light_mode
            title: context.loc.dark_theme, // Text from image
            trailing: Switch(
              value: false, // Placeholder value
              onChanged: (bool value) {
                // TODO: Implement Dark Theme Toggle Logic
                print('Dark Theme Toggled: $value');
              },
              activeColor: kPrimaryThemeColor,
            ),
            onTap: null, // Switch handles interaction
          ),
        ],
      ),
    );
  }

  Widget _buildMembershipList(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: _buildSettingsTile(
          context: context,
          icon: Icons.history, // Or Icons.payment
          title: context.loc.transaction_history, // Text from image
          trailing: const Icon(
            Icons.arrow_forward_ios,
            size: 16,
            color: Colors.grey,
          ),
          onTap: () {
            // TODO: Implement Payment History Navigation
            print('Payment History Tapped');
          },
        ),
      ),
    );
  }

  Widget _buildLogoutTile(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: InkWell(
        onTap: () async {
          // Call the logout method from the auth controller
          await ref.read(authControllerProvider.notifier).logout();
          // Navigate to splash screen after logout
          if (context.mounted) context.go(RouterName.splashScreen);
        },
        child: ListTile(
          leading: const Icon(Icons.logout, color: kPrimaryThemeColor),
          title: Text(
            context.loc.logout, // Text from image
            style: TextStyle(
              color: kPrimaryThemeColor,
              fontWeight: FontWeight.w500,
            ),
          ),
          contentPadding: const EdgeInsets.symmetric(
            vertical: 4.0,
            horizontal: 16.0,
          ),
        ),
      ),
    );
  }

  // Helper for creating consistent ListTiles
  Widget _buildSettingsTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required Widget? trailing,
    required VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: ListTile(
        leading: Icon(icon, color: kPrimaryThemeColor),
        title: Text(title, style: Theme.of(context).textTheme.bodyLarge),
        trailing: trailing,
        contentPadding: const EdgeInsets.symmetric(
          vertical: 4.0,
          horizontal: 16.0,
        ),
        visualDensity: VisualDensity.compact, // Adjust density if needed
      ),
    );
  }

  // Widget to display version information
  Widget _buildVersionInfo(BuildContext context) {
    return FutureBuilder<PackageInfo>(
      future: PackageInfo.fromPlatform(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
            child: LoadingCircle(),
          ); // Show loader (removed size)
        } else if (snapshot.hasError) {
          return const Center(
            child: Text(
              'Gagal memuat versi', // Error message
              style: TextStyle(color: Colors.red),
            ),
          );
        } else if (snapshot.hasData) {
          final packageInfo = snapshot.data!;
          final versionText =
              'Version ${packageInfo.version} build ${packageInfo.buildNumber}';
          return Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: Center(
              child: Text(
                versionText,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey.shade600),
              ),
            ),
          );
        } else {
          return const SizedBox.shrink(); // Return empty if no data
        }
      },
    );
  }
}
