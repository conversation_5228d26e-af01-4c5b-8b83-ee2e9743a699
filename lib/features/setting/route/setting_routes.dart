part of 'package:selfeng/configs/routes/core_routes.dart';

@TypedGoRoute<LanguageRoute>(
  path: '/language/:origin',
  name: RouterName.languageScreen,
)
class LanguageRoute extends GoRouteData with _$LanguageRoute {
  const LanguageRoute({required this.origin});
  final String origin;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      language.loadLibrary,
      () => language.LanguageScreen(origin: origin),
    );
  }
}

@TypedGoRoute<SelectedLanguageRoute>(
  path: "/language-selected",
  name: RouterName.selectedLanguageScreen,
)
class SelectedLanguageRoute extends GoRouteData with _$SelectedLanguageRoute {
  const SelectedLanguageRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      selected_language.loadLibrary,
      () => selected_language.SelectedLanguageScreen(),
    );
  }
}

@TypedGoRoute<ProfileSettingRoute>(
  path: '/profile-settings',
  name: RouterName.profilesettingScreen,
)
class ProfileSettingRoute extends GoRouteData with _$ProfileSettingRoute {
  const ProfileSettingRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      profile_setting.loadLibrary,
      () => profile_setting.ProfileSettingsScreen(),
    );
  }
}

@TypedGoRoute<CertificateListRoute>(
  path: '/certificate-list',
  name: RouterName.certificateListScreen,
)
class CertificateListRoute extends GoRouteData with _$CertificateListRoute {
  const CertificateListRoute();
  @override
  Widget build(BuildContext context, GoRouterState state) {
    return DeferredRoute(
      certificate_list.loadLibrary,
      () => certificate_list.CertificateListScreen(),
    );
  }
}
