import 'dart:async';
import 'package:flutter/foundation.dart' show kDebugMode;
import 'package:flutter/material.dart';
import 'package:record/record.dart';
import 'package:selfeng/shared/globals.dart';
import 'package:selfeng/shared/mixins/audio/audio_recorder_platform.dart';
import 'dart:math' as math;

class Recorder extends StatefulWidget {
  final void Function(String path) onStop;
  final void Function() onStart;

  const Recorder({super.key, required this.onStop, required this.onStart});

  @override
  State<Recorder> createState() => _RecorderState();
}

class _RecorderState extends State<Recorder> with AudioRecorderMixin {
  int _recordDuration = 0;
  Timer? _timer;
  late final AudioRecorder _audioRecorder;
  StreamSubscription<RecordState>? _recordSub;
  RecordState _recordState = RecordState.stop;
  StreamSubscription<Amplitude>? _amplitudeSub;
  bool _isInitializing = false; // Added for initializing state

  @override
  void initState() {
    _audioRecorder = AudioRecorder();
    // Permission.microphone.request();

    _recordSub = _audioRecorder.onStateChanged().listen((recordState) {
      _updateRecordState(recordState);
    });

    _amplitudeSub = _audioRecorder
        .onAmplitudeChanged(const Duration(milliseconds: 300))
        .listen((amp) {});

    super.initState();
  }

  Future<void> _start() async {
    // Prevent starting if already recording or initializing
    if (_recordState == RecordState.record || _isInitializing) {
      return;
    }

    widget.onStart(); // Call immediately

    setState(() {
      _isInitializing = true;
    });

    try {
      if (!await _audioRecorder.hasPermission()) {
        if (kDebugMode) print("Permission not granted");
        setState(() {
          _isInitializing = false;
        });
        // Optionally show a message to the user
        return;
      }

      const encoder = AudioEncoder.wav;
      if (!await _isEncoderSupported(encoder)) {
        if (kDebugMode) print("Encoder not supported");
        setState(() {
          _isInitializing = false;
        });
        // Optionally show a message to the user
        return;
      }

      // final devs = await _audioRecorder.listInputDevices(); // Potentially time-consuming
      // if (kDebugMode) {
      //   debugPrint(devs.toString());
      // }

      const config = RecordConfig(
        encoder: encoder,
        numChannels: 1,
        sampleRate: 16000,
        autoGain: true,
        noiseSuppress: false,
        echoCancel: false,
      );

      // Record to file. This should trigger an onStateChanged event.
      // If successful, onStateChanged will receive RecordState.record,
      // which in _updateRecordState will set _isInitializing = false and start the timer.
      await recordFile(_audioRecorder, config);
    } catch (e) {
      if (kDebugMode) {
        print('Error during _start: $e');
      }
      setState(() {
        _isInitializing = false;
        // Ensure state is reset, _updateRecordState might also be called if recorder goes to stop
        // Manually trigger stop state handling if not already stopped.
        if (_recordState != RecordState.stop) {
          _updateRecordState(RecordState.stop);
        } else {
          // If already stop, ensure UI is updated if it was stuck in initializing
          _recordState = RecordState.stop; // Explicitly ensure state
        }
      });
    }
  }

  Future<void> _stop() async {
    final path = await _audioRecorder.stop();

    if (path != null) {
      widget.onStop(path);

      downloadWebData(path);
    }
  }

  // Future<void> _pause() => _audioRecorder.pause();

  // Future<void> _resume() => _audioRecorder.resume();

  void _updateRecordState(RecordState recordState) {
    setState(() {
      _recordState = recordState;

      switch (recordState) {
        case RecordState.pause:
          _timer?.cancel();
          _isInitializing = false; // No longer initializing
          break;
        case RecordState.record:
          _recordDuration = 0; // Reset duration when recording actually starts
          _startTimer();
          _isInitializing = false; // No longer initializing
          break;
        case RecordState.stop:
          _timer?.cancel();
          _recordDuration = 0;
          _isInitializing = false; // No longer initializing
          break;
      }
    });
  }

  Future<bool> _isEncoderSupported(AudioEncoder encoder) async {
    final isSupported = await _audioRecorder.isEncoderSupported(encoder);

    if (!isSupported) {
      debugPrint('${encoder.name} is not supported on this platform.');
      debugPrint('Supported encoders are:');

      for (final e in AudioEncoder.values) {
        if (await _audioRecorder.isEncoderSupported(e)) {
          debugPrint('- ${encoder.name}');
        }
      }
    }

    return isSupported;
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const SizedBox(height: 4),
        _buildRecordStopControl(),
      ],
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _recordSub?.cancel();
    _amplitudeSub?.cancel();
    _audioRecorder.dispose();
    super.dispose();
  }

  Widget _buildRecordStopControl() {
    if (_isInitializing) {
      return Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.grey.withValues(alpha: .5), // Indicate loading
        ),
        width: MediaQuery.of(context).size.width - 72,
        height: 90, // Consistent height with non-recording state
        margin: const EdgeInsets.symmetric(horizontal: 37),
        padding: const EdgeInsets.symmetric(vertical: 11, horizontal: 16.5),
        child: const BouncingCirclesAnimation(
          circleColors: [
            Colors.white,
            Colors.white,
            Colors.white,
            Colors.white,
          ],
          circleSize: 10,
          bouncingHeight: 10,
        ), // Show loading animation
      );
    }

    return InkWell(
      onTap: () {
        if (_isInitializing) return; // Prevent action while initializing
        (_recordState != RecordState.stop) ? _stop() : _start();
      },
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color:
              _recordState != RecordState.stop
                  ? const Color(
                    0xffF5BE48,
                  ) // Color when recording (tap to stop)
                  : const Color(
                    0xffC00017,
                  ), // Color when stopped (tap to start)
          boxShadow: [
            if(_recordState == RecordState.stop)
            BoxShadow(
              color: Color(0xffFF897F),
              spreadRadius: 0,
              blurRadius: 30,
              offset: Offset(0, 4), // changes position of shadow
            ),
          ],
        ),
        width: MediaQuery.of(context).size.width - 72,
        height: _recordState != RecordState.stop ? 110 : 90,
        margin: const EdgeInsets.symmetric(horizontal: 37),
        padding: const EdgeInsets.symmetric(vertical: 11, horizontal: 16.5),
        child: Icon(
          Icons.mic_rounded,
          size: 48,
          color: Colors.white,
        ),
      ),
    );
  }

  void _startTimer() {
    _timer?.cancel();

    _timer = Timer.periodic(const Duration(seconds: 1), (Timer t) {
      setState(() => _recordDuration++);
    });
  }
}

class BouncingCirclesAnimation extends StatefulWidget {
  final List<Color> circleColors;
  final double circleSize;
  final double bouncingHeight;
  final double spacing;

  const BouncingCirclesAnimation({
    super.key,
    this.circleColors = const [
      Colors.white,
      Colors.white,
      Colors.white,
      Colors.white,
    ],
    this.circleSize = 10,
    this.bouncingHeight = 10,
    this.spacing = 6,
  });

  @override
  _BouncingCirclesAnimationState createState() =>
      _BouncingCirclesAnimationState();
}

class _BouncingCirclesAnimationState extends State<BouncingCirclesAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          painter: BouncingCirclesPainter(
            animation: _controller,
            circleColors: widget.circleColors,
            circleSize: widget.circleSize,
            bouncingHeight: widget.bouncingHeight,
            spacing: widget.spacing,
          ),
          size: Size(
            widget.circleSize * 4 + widget.spacing * 3,
            widget.bouncingHeight + widget.circleSize,
          ),
        );
      },
    );
  }
}

class BouncingCirclesPainter extends CustomPainter {
  final Animation<double> animation;
  final List<Color> circleColors;
  final double circleSize;
  final double bouncingHeight;
  final double spacing;

  BouncingCirclesPainter({
    required this.animation,
    required this.circleColors,
    required this.circleSize,
    required this.bouncingHeight,
    required this.spacing,
  }) : super(repaint: animation);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    final circleRadius = circleSize / 2;

    for (int i = 0; i < 4; i++) {
      paint.color = circleColors[i];

      // Calculate phase offset for each circle
      final phaseOffset = i * 0.25;
      final yOffset =
          math.sin((animation.value + phaseOffset) * 2 * math.pi) *
          bouncingHeight /
          2;

      final centerX = i * (circleSize + spacing) + circleRadius;
      final centerY = size.height / 2 - yOffset;

      canvas.drawCircle(Offset(centerX, centerY), circleRadius, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
