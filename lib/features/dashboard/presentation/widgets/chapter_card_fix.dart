import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:selfeng/shared/domain/models/level.dart';
import 'package:selfeng/shared/widgets/loading_circle.dart';

class ChapterCardFix extends StatelessWidget {
  final int chapter;
  final String title;
  final String imageUrl;
  final Level level;
  final String route;
  final VoidCallback onTap;

  const ChapterCardFix({
    required this.chapter,
    required this.title,
    required this.imageUrl,
    required this.level,
    required this.route,
    required this.onTap,
    super.key,
  });

  // --- Constants copied from ChapterCard for visual consistency ---
  static const _shadowStyle = Shadow(
    offset: Offset(2.0, 2.0),
    blurRadius: 5.0,
    color: Color.fromARGB(255, 0, 0, 0),
  );

  static const _textStyle = TextStyle(
    fontWeight: FontWeight.bold,
    color: Colors.white,
    shadows: [_shadowStyle],
  );

  static const _boxShadow = BoxShadow(
    color: Color.fromARGB(64, 189, 186, 186),
    spreadRadius: 0,
    blurRadius: 10.3,
    offset: Offset(0, 1),
  );

  static const _padding = EdgeInsets.all(8.0);
  static const _titlePadding = EdgeInsets.only(left: 10, right: 10, top: 14);
  static const _footerPadding = EdgeInsets.only(
    left: 10,
    right: 10,
    bottom: 14,
  );
  static const _containerSize = Size(168, 152);
  static const _borderRadius = BorderRadius.all(Radius.circular(10));
  static const _iconSize = 16.0;
  static const _titleSpacing = SizedBox(height: 8);
  // --- End of copied constants ---

  @override
  Widget build(BuildContext context) {
    // Use RepaintBoundary for performance optimization
    return RepaintBoundary(
      // Apply the outer padding
      child: Padding(
        padding: _padding,
        // Container for applying the box shadow
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: _borderRadius,
            border: Border.all(color: const Color(0xffD0C4C2)),
            // boxShadow: [_boxShadow],
          ),
          width: _containerSize.width,
          height: _containerSize.height,
          // InkWell for tap interaction, placed inside shadow container
          child: InkWell(
            onTap: onTap,
            borderRadius:
                _borderRadius, // Ensure ripple effect respects border radius
            // ClipRRect to enforce rounded corners on the content (Stack)
            child: Column(
              children: [
                // --- Background Image ---
                ClipRRect(
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10)),
                  child: CachedNetworkImage(
                    imageUrl: imageUrl,
                    width: _containerSize.width,
                    height: _containerSize.height * 0.7,
                    memCacheWidth: _containerSize.width.toInt(),
                    // memCacheHeight: _containerSize.height.toInt(),
                    fit: BoxFit.fitWidth,
                    // Placeholder while loading
                    placeholder:
                        (context, url) => Container(
                      color: Colors.grey[300], // Placeholder background
                      child: const Center(child: LoadingCircle()),
                    ),
                    // Error widget if image fails to load
                    errorWidget:
                        (context, url, error) => Container(
                      color: Colors.grey[300], // Error background
                      child: const Center(child: Icon(Icons.error)),
                    ),
                  ),
                ),
                // --- Darkening Overlay ---
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(
                      alpha: 0.3,
                    ), // Apply darkening effect
                    borderRadius: BorderRadius.circular(
                      16.0,
                    ), // Match clipping
                  ),
                ),
                Container(
                  padding: _titlePadding,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _titleSpacing,
                      Text('Chapter $chapter', style: Theme.of(context).textTheme.labelLarge,),
                      _titleSpacing,
                      _buildInfoRow(
                          Icons.copy_rounded,
                          '4 Skills',
                          Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: Color(0xffB4A9A7)
                          )
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String text, TextStyle? textStyle) {
    return Row(
      children: [
        Icon(
          icon,
          size: _iconSize,
          color: Color(0xffB4A9A7),
        ),
        const SizedBox(width: 4), // Spacing between icon and text
        Text(text, style: textStyle),
      ],
    );
  }

  // --- End of copied helper methods ---
}
